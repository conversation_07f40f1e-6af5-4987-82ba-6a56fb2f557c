import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Instagram from './pages/Instagram';
import Dashboard from './pages/Dashboard';
import Login from './pages/Login';
import Register from './pages/Register';
import Account from './pages/Account';

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/instagram" element={<Instagram/>} />
        <Route path="/dashboard" element={<Dashboard/>} />
        <Route path="/login" element={<Login/>} />
        <Route path="/register" element={<Register/>} />
        <Route path="/account" element={<Account/>} />
        <Route path="/" element={<Dashboard/>} />
      </Routes>
    </Router>
  );
}

export default App;
