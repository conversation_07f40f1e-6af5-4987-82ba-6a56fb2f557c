import React from 'react';
import { useLocation } from 'react-router-dom';
import { Sidebar, SidebarHeader, SidebarItem, SidebarBody, SidebarFooter, SidebarLabel, SidebarSpacer } from './sidebar';
import { Dropdown } from './dropdown';
import { HomeIcon, LightBulbIcon, ArrowLeftEndOnRectangleIcon, UserCircleIcon } from '@heroicons/react/20/solid';
import * as Headless from '@headlessui/react';

function MainSidebar() {
  const location = useLocation();
  const currentPath = location.pathname;
  return (
    <Sidebar>
      <SidebarHeader>
        <Dropdown>
          <Headless.MenuButton
            className="flex w-full items-center gap-3 rounded-xl border border-transparent p-1 data-active:border-zinc-200 data-hover:border-zinc-200 dark:data-active:border-zinc-700 dark:data-hover:border-zinc-700"
            aria-label="Account options">
            <span className="block text-left content">
              <span className="block text-sm dark:text-white font-medium">Liam</span>
              <span className="block text-xs  text-zinc-500"><EMAIL></span>
            </span>
          </Headless.MenuButton>
        </Dropdown>
      </SidebarHeader>

      <SidebarBody>
        <SidebarItem
          href="/dashboard"
          current={currentPath === '/dashboard' || currentPath === '/'}
        >
          <HomeIcon />
          <SidebarLabel>Dashboard</SidebarLabel>
        </SidebarItem>
        <SidebarSpacer>
          <SidebarItem
            className="items-center"
            href="/instagram"
            current={currentPath === '/instagram'}
          >
            <img className="size-5" src={"src/assets/Instagram.svg"} />
            <SidebarLabel>Instagram</SidebarLabel>
          </SidebarItem>
          <SidebarItem
            className="items-center"
            href="/whatsapp"
            current={currentPath === '/whatsapp'}
          >
            <img className="size-5" src={"src/assets/Whatsapp.svg"} />
            <SidebarLabel>Whatsapp</SidebarLabel>
          </SidebarItem>
          <SidebarItem
            className="items-center"
            href="/facebook"
            current={currentPath === '/facebook'}
          >
            <img className="size-5" src={"src/assets/Facebook.svg"} />
            <SidebarLabel>Facebook</SidebarLabel>
          </SidebarItem>
          <SidebarItem
            className="items-center"
            href="/linkedin"
            current={currentPath === '/linkedin'}
          >
            <img className="size-5" src={"src/assets/Linkedin.svg"} />
            <SidebarLabel>LinkedIn</SidebarLabel>
          </SidebarItem>
        </SidebarSpacer>
      </SidebarBody>

      <SidebarFooter>
        <SidebarItem
          className="items-center"
          href="/account"
          current={currentPath === '/account'}
        >
          <UserCircleIcon />
          <SidebarLabel>Compte</SidebarLabel>
        </SidebarItem>
        <SidebarItem
          className="items-center"
          href="/tutorials"
          current={currentPath === '/tutorials'}
        >
          <LightBulbIcon />
          <SidebarLabel>Tutoriels</SidebarLabel>
        </SidebarItem>
        <SidebarItem
          className="items-center mb-6"
          href="/left"
          current={currentPath === '/left'}
        >
          <ArrowLeftEndOnRectangleIcon />
          <SidebarLabel>Déconnexion</SidebarLabel>
        </SidebarItem>
        <img src={"src/assets/Frame 41.svg"} />
      </SidebarFooter>
    </Sidebar>
  );
}

export default MainSidebar;
