import React from 'react';

export function BarChart({ title, data, color = 'blue', maxValue = null }) {
  const colorClasses = {
    blue: 'bg-blue-500 dark:bg-blue-600',
    green: 'bg-green-500 dark:bg-green-600',
    red: 'bg-red-500 dark:bg-red-600',
    purple: 'bg-purple-500 dark:bg-purple-600',
    yellow: 'bg-yellow-500 dark:bg-yellow-600',
  };

  // Calculate the maximum value for scaling if not provided
  const calculatedMaxValue = maxValue || Math.max(...data.map(item => item.value)) * 1.2;

  return (
    <div className="rounded-lg bg-white p-6 shadow-sm ring-1 ring-zinc-950/5 dark:bg-zinc-900 dark:ring-white/10">
      <h3 className="mb-4 text-lg font-medium text-zinc-900 dark:text-white">{title}</h3>
      <div className="mt-6 space-y-2">
        {data.map((item, index) => (
          <div key={index} className="flex items-center">
            <span className="w-20 text-sm text-zinc-600 dark:text-zinc-400">{item.label}</span>
            <div className="flex-1">
              <div className="h-8 w-full bg-zinc-100 dark:bg-zinc-800 rounded-md overflow-hidden">
                <div
                  className={`h-full ${colorClasses[color]} rounded-md`}
                  style={{ width: `${(item.value / calculatedMaxValue) * 100}%` }}
                ></div>
              </div>
            </div>
            <span className="ml-2 w-12 text-right text-sm font-medium text-zinc-900 dark:text-white">
              {item.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
}
