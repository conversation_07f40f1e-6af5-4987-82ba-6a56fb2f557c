import React from 'react';

export function DonutChart({ title, percentage, color = 'blue', size = 'md' }) {
  const colorClasses = {
    blue: 'text-blue-600 dark:text-blue-400',
    green: 'text-green-600 dark:text-green-400',
    red: 'text-red-600 dark:text-red-400',
    purple: 'text-purple-600 dark:text-purple-400',
    yellow: 'text-yellow-600 dark:text-yellow-400',
  };

  const sizeClasses = {
    sm: 'w-16 h-16',
    md: 'w-24 h-24',
    lg: 'w-32 h-32',
  };

  const circumference = 2 * Math.PI * 40; // r = 40
  const strokeDasharray = circumference;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="rounded-lg bg-white p-6 shadow-sm ring-1 ring-zinc-950/5 dark:bg-zinc-900 dark:ring-white/10">
      <h3 className="mb-4 text-lg font-medium text-zinc-900 dark:text-white">{title}</h3>
      <div className="flex items-center justify-between">
        <div className={`relative ${sizeClasses[size]} flex items-center justify-center`}>
          <svg className="w-full h-full" viewBox="0 0 100 100">
            {/* Background circle */}
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="currentColor"
              className="text-zinc-100 dark:text-zinc-800"
              strokeWidth="10"
            />
            {/* Foreground circle */}
            <circle
              cx="50"
              cy="50"
              r="40"
              fill="none"
              stroke="currentColor"
              className={colorClasses[color]}
              strokeWidth="10"
              strokeDasharray={strokeDasharray}
              strokeDashoffset={strokeDashoffset}
              strokeLinecap="round"
              transform="rotate(-90 50 50)"
            />
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <span className={`text-xl font-bold ${colorClasses[color]}`}>{percentage}%</span>
          </div>
        </div>
        <div className="ml-4">
          <div className="space-y-2">
            <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full ${colorClasses[color].replace('text-', 'bg-')}`}></div>
              <span className="ml-2 text-sm text-zinc-600 dark:text-zinc-400">Taux de conversion</span>
            </div>
            <div className="flex items-center">
              <div className="h-3 w-3 rounded-full bg-zinc-200 dark:bg-zinc-700"></div>
              <span className="ml-2 text-sm text-zinc-600 dark:text-zinc-400">Sans réponse</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
