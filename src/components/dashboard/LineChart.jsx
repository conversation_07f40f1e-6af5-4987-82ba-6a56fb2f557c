import React from 'react';

export function LineChart({ title, data, color = 'blue' }) {
  const colorClasses = {
    blue: 'stroke-blue-500 dark:stroke-blue-400',
    green: 'stroke-green-500 dark:stroke-green-400',
    red: 'stroke-red-500 dark:stroke-red-400',
    purple: 'stroke-purple-500 dark:stroke-purple-400',
    yellow: 'stroke-yellow-500 dark:stroke-yellow-400',
  };

  const dotColorClasses = {
    blue: 'fill-blue-500 dark:fill-blue-400',
    green: 'fill-green-500 dark:fill-green-400',
    red: 'fill-red-500 dark:fill-red-400',
    purple: 'fill-purple-500 dark:fill-purple-400',
    yellow: 'fill-yellow-500 dark:fill-yellow-400',
  };

  // Find min and max values for scaling
  const values = data.map(item => item.value);
  const minValue = Math.min(...values);
  const maxValue = Math.max(...values);
  const range = maxValue - minValue;
  
  // Padding to avoid points at the very top or bottom
  const padding = range * 0.1;
  const adjustedMin = Math.max(0, minValue - padding);
  const adjustedMax = maxValue + padding;
  const adjustedRange = adjustedMax - adjustedMin;

  // Calculate points for the SVG path
  const width = 300;
  const height = 150;
  const points = data.map((item, index) => {
    const x = (index / (data.length - 1)) * width;
    const y = height - ((item.value - adjustedMin) / adjustedRange) * height;
    return { x, y, ...item };
  });

  // Create the SVG path
  const pathData = points.reduce((path, point, index) => {
    const command = index === 0 ? 'M' : 'L';
    return `${path} ${command} ${point.x},${point.y}`;
  }, '');

  return (
    <div className="rounded-lg bg-white p-6 shadow-sm ring-1 ring-zinc-950/5 dark:bg-zinc-900 dark:ring-white/10">
      <h3 className="mb-4 text-lg font-medium text-zinc-900 dark:text-white">{title}</h3>
      <div className="mt-6">
        <svg width={width} height={height} className="overflow-visible">
          {/* Grid lines */}
          {[0, 1, 2, 3].map((line, index) => (
            <line
              key={index}
              x1="0"
              y1={height - (height * line) / 3}
              x2={width}
              y2={height - (height * line) / 3}
              stroke="currentColor"
              className="text-zinc-200 dark:text-zinc-700"
              strokeDasharray="4,4"
            />
          ))}
          
          {/* Line path */}
          <path
            d={pathData}
            fill="none"
            className={`${colorClasses[color]} stroke-2`}
          />
          
          {/* Data points */}
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="4"
              className={`${dotColorClasses[color]}`}
            />
          ))}
        </svg>
        
        {/* X-axis labels */}
        <div className="mt-2 flex justify-between">
          {data.map((item, index) => (
            <div key={index} className="text-xs text-zinc-500 dark:text-zinc-400">
              {item.label}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
