import React from 'react';

export function MessageStats({ messages, responses }) {
  const responseRate = messages > 0 ? Math.round((responses / messages) * 100) : 0;
  
  return (
    <div className="rounded-lg bg-white p-6 shadow-sm ring-1 ring-zinc-950/5 dark:bg-zinc-900 dark:ring-white/10">
      <h3 className="mb-4 text-lg font-medium text-zinc-900 dark:text-white">Messages et Réponses</h3>
      <div className="grid grid-cols-3 gap-4">
        <div className="text-center">
          <p className="text-sm text-zinc-500 dark:text-zinc-400">Messages envoyés</p>
          <p className="mt-1 text-2xl font-semibold text-zinc-900 dark:text-white">{messages}</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-zinc-500 dark:text-zinc-400">Réponses reçues</p>
          <p className="mt-1 text-2xl font-semibold text-zinc-900 dark:text-white">{responses}</p>
        </div>
        <div className="text-center">
          <p className="text-sm text-zinc-500 dark:text-zinc-400">Taux de réponse</p>
          <p className="mt-1 text-2xl font-semibold text-zinc-900 dark:text-white">{responseRate}%</p>
        </div>
      </div>
      <div className="mt-4 h-4 w-full bg-zinc-100 dark:bg-zinc-800 rounded-full overflow-hidden">
        <div
          className="h-full bg-blue-500 dark:bg-blue-600 rounded-full"
          style={{ width: `${responseRate}%` }}
        ></div>
      </div>
    </div>
  );
}
