import React from 'react';
import { Input } from '../components/input';
import { Button } from '../components/button';
import { Divider } from '../components/divider';
import { Avatar } from '../components/avatar';

const RegisterPage = () => {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md rounded-xl bg-white shadow-lg p-8 space-y-6">
        <div className="flex justify-center">
          <Avatar src="src/assets/Group 12.png" className="h-12 w-12" />
        </div>
        <h2 className="text-2xl font-semibold text-center">Créer un compte</h2>

        <form className="space-y-4">
          <Input type="text" placeholder="Nom complet" required />
          <Input type="email" placeholder="Email" required />
          <Input type="password" placeholder="Mot de passe" required />
          <Input type="password" placeholder="Confirmer le mot de passe" required />

          <Button className="w-full" type="submit">
            S'inscrire
          </Button>
        </form>

        <Divider />

        <div className="text-sm text-center">
          <span className="text-gray-600">Déjà inscrit ?</span>{' '}
          <a href="/login" className="text-blue-600 hover:underline">
            Se connecter
          </a>
        </div>
      </div>
    </div>
  );
};

export default RegisterPage;