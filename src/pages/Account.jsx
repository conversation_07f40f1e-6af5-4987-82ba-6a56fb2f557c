import React from 'react';
import { SidebarLayout } from '../components/sidebar-layout';
import MainSidebar from '../components/MainSidebar';
import { Avatar } from '../components/avatar';
import { Input } from '../components/input';
import { Dropdown, DropdownButton, DropdownMenu, DropdownItem } from '../components/dropdown';
import { Select } from '../components/select';
import { Listbox, ListboxDescription, ListboxLabel, ListboxOption } from '../components/listbox';
import { CheckboxGroup, CheckboxField, Checkbox } from '../components/checkbox';
import { Badge } from '../components/badge';
import { Switch, SwitchField, SwitchGroup } from '../components/switch';
import { Label, Description, Field } from '../components/fieldset';
import { Divider } from '../components/divider';
import { Button } from '../components/button';
import { Strong, Text } from '../components/text';
import { HomeIcon, PlayIcon, PlusIcon, TrashIcon, LightBulbIcon, ArrowLeftEndOnRectangleIcon, UserCircleIcon } from '@heroicons/react/20/solid';
import * as Headless from '@headlessui/react';

const Page = () => {
  return (
    <SidebarLayout
      sidebar={<MainSidebar />}
    >
      <div className="space-y-6">
        <div className='flex place-content-between'>
          <h1 className="text-2xl font-semibold dark:text-white">Votre compte</h1>
        </div>
        <Divider />
        <div>
          <h2 className="text-xl font-semibold dark:text-white mb-3">Vos informations</h2>
          <Headless.Field className="mb-2">
            <Label>Email</Label>
            <Input className="max-w-72" placeholder="<EMAIL>" />
          </Headless.Field>
          <div className='flex gap-4'>
            <Headless.Field className="mb-2">
              <Label>Prénom</Label>
              <Input className="max-w-72" placeholder="Liam" />
            </Headless.Field>
            <Headless.Field className="mb-2">
              <Label>Nom</Label>
              <Input className="max-w-72" placeholder="Nisbet" />
            </Headless.Field>
          </div>
        </div>

        <div>
          <h2 className="text-xl font-semibold dark:text-white">Votre abonnement</h2>
          <Text className="mb-3">Votre prochain paiment aura lieu le <Strong>Date de Prélèvement</Strong></Text>
          <div className="flex gap-4 grid-cols-3 mb-3">
            <div className='w-full h-80 bg-zinc-800 rounded-lg p-4'>
            <div className='flex justify-between gap-4 mb-7'>
                <p className="text-lg font-semibold dark:text-white">Essentiel</p>
              </div>
              <div class="list-disc">
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
              </div>
            </div>
            <div className='w-full h-80 bg-zinc-800 rounded-lg p-4'>
              <div className='flex justify-between gap-4 mb-7'>
                <p className="text-lg font-semibold dark:text-white">Pro</p>
                <Badge color="green">Votre offre actuelle</Badge>
              </div>
              <div class="list-disc">
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
              </div>
            </div>
            <div className='w-full h-80 bg-zinc-800 rounded-lg p-4'>
            <div className='flex justify-between gap-4 mb-7'>
                <p className="text-lg font-semibold dark:text-white">Ultime</p>
              </div>
              <div class="list-disc">
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
                <Text>• test</Text>
              </div>
            </div>
          </div>
          <div className='flex gap-4'>
            <Button className="w-fit">Changer de moyen de paiment</Button>
            <Button color="red" className="w-fit">Résilier mon abonnement</Button>
          </div>
        </div>
      </div>
    </SidebarLayout>
  );
};

export default Page;