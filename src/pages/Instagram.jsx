import React from 'react';
import { SidebarLayout } from '../components/sidebar-layout';
import MainSidebar from '../components/MainSidebar';
import { Avatar } from '../components/avatar';
import { Input } from '../components/input';
import { Dropdown, DropdownButton, DropdownMenu, DropdownItem } from '../components/dropdown';
import { Select } from '../components/select';
import { Listbox, ListboxDescription, ListboxLabel, ListboxOption } from '../components/listbox';
import { CheckboxGroup, CheckboxField, Checkbox } from '../components/checkbox';
import { Badge } from '../components/badge';
import { Switch, SwitchField, SwitchGroup } from '../components/switch';
import { Label, Description, Field } from '../components/fieldset';
import { Divider } from '../components/divider';
import { Button } from '../components/button';
import { Text } from '../components/text';
import { HomeIcon, PlayIcon, PlusIcon, TrashIcon } from '@heroicons/react/20/solid';
import * as Headless from '@headlessui/react';

const Page = () => {
  return (
    <SidebarLayout
      sidebar={<MainSidebar />}
    >
      <div className="space-y-6">
        <div className='flex place-content-between'>
          <h1 className="text-2xl font-semibold dark:text-white">Instagram</h1>
          <div className='flex gap-2'>
            <Button color="green"><PlayIcon /></Button>
            <Button color="red"><TrashIcon /></Button>
            <Field>
              <Listbox className="w-56" placeholder="Campagne">
                <ListboxOption>Campagne</ListboxOption>
              </Listbox>
            </Field>
            <Button outline><PlusIcon /></Button>
          </div>
        </div>
        <Divider />
        <div>
          <h2 className="text-xl font-semibold dark:text-white">Étape 1</h2>
          <p className="text-sm text-gray-500 mb-3">Connexion au compte Instagram</p>
          <Listbox className="max-w-40">
            <ListboxOption>
              <Avatar src={"src/assets/Group 12.png"}></Avatar>
              <ListboxLabel className='w-fit'>Liam_Nisbet</ListboxLabel>
            </ListboxOption>
          </Listbox>
        </div>

        <div>
          <h2 className="text-xl font-semibold dark:text-white">Étape 2</h2>
          <p className="text-sm text-gray-500 mb-3">Sélection du message envoyé et tag campagne</p>
          <div className="flex gap-4">
            <Headless.Field>
              <Label>Message</Label>
              <Listbox className="w-52 max-w-52" placeholder="Message de prospection">
                <ListboxOption>Message de prospection</ListboxOption>
              </Listbox>
            </Headless.Field>
            <Headless.Field>
              <Label>Tag</Label>
              <Listbox className="w-52 max-w-52" placeholder="Prospection 1">
                <ListboxOption>Prospection 1</ListboxOption>
              </Listbox>
            </Headless.Field>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold dark:text-white">Étape 3</h2>
            <p className="text-sm text-gray-500 mb-3">Fréquence de la campagne</p>
            <CheckboxGroup className="space-y-1 dark:text-white">
              {["Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche"].map(day => (
                <CheckboxField key={day} className="gap-2"><Checkbox />{day}</CheckboxField>
              ))}
            </CheckboxGroup>
          </div>

          <div>
            <h2 className="text-xl font-semibold dark:text-white">Étape 4</h2>
            <p className="text-sm text-gray-500 mb-3">Comptes et hashtags de référence</p>
            <div className="flex gap-2 flex-wrap">
              {["@alqassemfamily", "@bigeyesfalcon", "@akhychalghoumi", "@gohancalvo", "@maurel_antonio", "@brazil_broly", "@kevinfaaages", "@shaggy_fusion_ultra_instinct"].map(tag => (
                <Badge key={tag}>{tag}</Badge>
              ))}
              <Badge>+ Ajouter</Badge>
            </div>
          </div>
        </div>

        <Divider />

        <div>
          <h2 className="text-xl font-semibold mb-3 dark:text-white">Filtres</h2>
          <div className='grid grid-cols-2  dark:text-white'>
            <SwitchGroup>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>N'envoyer de messages qu'aux nouvelles personnes</label>
                <Description className='w-96'>Si ce réglage est activé, Anyset n'enverra que des messages à des prospects jamais contactées.</Description>
              </Headless.Field>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>Filtrer par abonnements</label>
                <Description className='w-96'>Si ce réglage est activé, la recherche ne prendra en compte uniquement les prospects entre les deux valeurs.</Description>
                <div className='flex gap-3 mt-3 items-center'>
                  <Field>
                    <Input className="max-w-24" placeholder="100" pattern="[0-9]*" />
                  </Field>
                  <Text>à</Text>
                  <Field>
                    <Input className="max-w-24" placeholder="10000" pattern="[0-9]*" />
                  </Field>
                </div>
              </Headless.Field>
            </SwitchGroup>
            <SwitchGroup>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>Liker les posts</label>
                <Description className='w-96'>Si ce réglage est activé, Anyset likera des posts similaires à votre cible pour optimiser la qualité des prospects.</Description>
              </Headless.Field>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>Filtrer par mots clés</label>
                <Description className='w-96'>Si ce réglage est activé, Anyset contactera uniquement les prospects qui ont les mots spécifiés en bio.</Description>
                <div className="mt-3 flex gap-2 flex-wrap">
                  {["Amour", "Pieds", "Viande", "Arbre", "Téton"].map(tag => (
                    <Badge key={tag}>{tag}</Badge>
                  ))}
                  <Badge>+ Ajouter</Badge>
                </div>
              </Headless.Field>
            </SwitchGroup>
          </div>
        </div>
      </div>
    </SidebarLayout>
  );
};

export default Page;