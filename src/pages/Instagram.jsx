import React, { useState } from 'react';
import { SidebarLayout } from '../components/sidebar-layout';
import MainSidebar from '../components/MainSidebar';
import { Avatar } from '../components/avatar';
import { Input } from '../components/input';
import { Dropdown, DropdownButton, DropdownMenu, DropdownItem } from '../components/dropdown';
import { Select } from '../components/select';
import { Listbox, ListboxDescription, ListboxLabel, ListboxOption } from '../components/listbox';
import { CheckboxGroup, CheckboxField, Checkbox } from '../components/checkbox';
import { Badge, BadgeButton } from '../components/badge';
import { Switch, SwitchField, SwitchGroup } from '../components/switch';
import { Label, Description, Field } from '../components/fieldset';
import { Divider } from '../components/divider';
import { Button } from '../components/button';
import { Text } from '../components/text';
import { HomeIcon, PlayIcon, PlusIcon, TrashIcon, StopIcon } from '@heroicons/react/20/solid';
import * as Headless from '@headlessui/react';

const Page = () => {
  const [isRunning, setIsRunning] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // États pour les comptes et hashtags de référence
  const [referenceAccounts, setReferenceAccounts] = useState([
    "@alqassemfamily", "@bigeyesfalcon", "@akhychalghoumi", "@gohancalvo",
    "@maurel_antonio", "@brazil_broly", "@kevinfaaages", "@shaggy_fusion_ultra_instinct"
  ]);
  const [showAddInput, setShowAddInput] = useState(false);
  const [newAccountInput, setNewAccountInput] = useState("");

  // États pour les mots clés
  const [keywords, setKeywords] = useState(["Amour", "Pieds", "Viande", "Arbre", "Téton"]);
  const [showKeywordInput, setShowKeywordInput] = useState(false);
  const [newKeywordInput, setNewKeywordInput] = useState("");

  const handleStart = async () => {
    setIsLoading(true);
    // Simuler un délai de démarrage
    setTimeout(() => {
      setIsRunning(true);
      setIsLoading(false);
    }, 2000);
  };

  const handleStop = () => {
    setIsRunning(false);
  };

  // Fonctions pour gérer les comptes de référence
  const handleAddAccount = () => {
    if (newAccountInput.trim()) {
      let account = newAccountInput.trim();
      // Ajouter @ au début si pas présent
      if (!account.startsWith('@')) {
        account = '@' + account;
      }
      setReferenceAccounts([...referenceAccounts, account]);
      setNewAccountInput("");
      setShowAddInput(false);
    }
  };

  const handleRemoveAccount = (accountToRemove) => {
    setReferenceAccounts(referenceAccounts.filter(account => account !== accountToRemove));
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddAccount();
    } else if (e.key === 'Escape') {
      setShowAddInput(false);
      setNewAccountInput("");
    }
  };

  // Fonctions pour gérer les mots clés
  const handleAddKeyword = () => {
    if (newKeywordInput.trim()) {
      setKeywords([...keywords, newKeywordInput.trim()]);
      setNewKeywordInput("");
      setShowKeywordInput(false);
    }
  };

  const handleRemoveKeyword = (keywordToRemove) => {
    setKeywords(keywords.filter(keyword => keyword !== keywordToRemove));
  };

  const handleKeywordKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleAddKeyword();
    } else if (e.key === 'Escape') {
      setShowKeywordInput(false);
      setNewKeywordInput("");
    }
  };
  return (
    <SidebarLayout
      sidebar={<MainSidebar />}
    >
      <div className="space-y-6">
        <div className='flex place-content-between'>
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-semibold dark:text-white">Instagram</h1>
            {isRunning && (
              <Badge color="green" className="animate-pulse">
                En cours d'exécution
              </Badge>
            )}
            {isLoading && (
              <Badge color="yellow" className="animate-pulse">
                Démarrage...
              </Badge>
            )}
          </div>
          <div className='flex gap-2'>
            {!isRunning ? (
              <Button
                color="green"
                onClick={handleStart}
                disabled={isLoading}
                className={isLoading ? "opacity-50 cursor-not-allowed" : ""}
              >
                {isLoading ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                    <span className="text-sm">Démarrage...</span>
                  </div>
                ) : (
                  <>
                    <PlayIcon className="w-4 h-4" />
                    <span className="text-sm">Démarrer</span>
                  </>
                )}
              </Button>
            ) : (
              <Button
                color="red"
                onClick={handleStop}
              >
                <StopIcon className="w-4 h-4" />
                <span className="text-sm">Arrêter</span>
              </Button>
            )}
            <Button color="red" disabled={isRunning || isLoading}>
              <TrashIcon className="w-4 h-4" />
            </Button>
            <Field>
              <Listbox className="w-56" placeholder="Campagne" disabled={isRunning || isLoading}>
                <ListboxOption>Campagne</ListboxOption>
              </Listbox>
            </Field>
            <Button outline disabled={isRunning || isLoading}>
              <PlusIcon className="w-4 h-4" />
            </Button>
          </div>
        </div>
        <Divider />
        <div>
          <h2 className="text-xl font-semibold dark:text-white">Étape 1</h2>
          <p className="text-sm text-gray-500 mb-3">Connexion au compte Instagram</p>
          <Listbox className="max-w-40">
            <ListboxOption>
              <Avatar src={"src/assets/Group 12.png"}></Avatar>
              <ListboxLabel className='w-fit'>Liam_Nisbet</ListboxLabel>
            </ListboxOption>
          </Listbox>
        </div>

        <div>
          <h2 className="text-xl font-semibold dark:text-white">Étape 2</h2>
          <p className="text-sm text-gray-500 mb-3">Sélection du message envoyé et tag campagne</p>
          <div className="flex gap-4">
            <Headless.Field>
              <Label>Message</Label>
              <Listbox className="w-52 max-w-52" placeholder="Message de prospection">
                <ListboxOption>Message de prospection</ListboxOption>
              </Listbox>
            </Headless.Field>
            <Headless.Field>
              <Label>Tag</Label>
              <Listbox className="w-52 max-w-52" placeholder="Prospection 1">
                <ListboxOption>Prospection 1</ListboxOption>
              </Listbox>
            </Headless.Field>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-8">
          <div>
            <h2 className="text-xl font-semibold dark:text-white">Étape 3</h2>
            <p className="text-sm text-gray-500 mb-3">Fréquence de la campagne</p>
            <CheckboxGroup className="space-y-1 dark:text-white">
              {["Lundi", "Mardi", "Mercredi", "Jeudi", "Vendredi", "Samedi", "Dimanche"].map(day => (
                <CheckboxField key={day} className="gap-2"><Checkbox />{day}</CheckboxField>
              ))}
            </CheckboxGroup>
          </div>

          <div>
            <h2 className="text-xl font-semibold dark:text-white">Étape 4</h2>
            <p className="text-sm text-gray-500 mb-3">Comptes et hashtags de référence</p>
            <div className="flex gap-2 flex-wrap items-center">
              {referenceAccounts.map(account => (
                <BadgeButton
                  key={account}
                  color="zinc"
                  onClick={() => handleRemoveAccount(account)}
                  className="hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors"
                  title="Cliquer pour supprimer"
                >
                  {account}
                </BadgeButton>
              ))}
              {showAddInput ? (
                <div className="flex items-center gap-2">
                  <Input
                    value={newAccountInput}
                    onChange={(e) => setNewAccountInput(e.target.value)}
                    onKeyDown={handleKeyPress}
                    placeholder="@nom_compte"
                    className="w-32 text-sm"
                    autoFocus
                  />
                  <Button
                    color="green"
                    onClick={handleAddAccount}
                    className="text-xs px-2 py-1"
                  >
                    ✓
                  </Button>
                  <Button
                    color="red"
                    onClick={() => {
                      setShowAddInput(false);
                      setNewAccountInput("");
                    }}
                    className="text-xs px-2 py-1"
                  >
                    ✕
                  </Button>
                </div>
              ) : (
                <BadgeButton
                  color="blue"
                  onClick={() => setShowAddInput(true)}
                  className="hover:bg-blue-100 dark:hover:bg-blue-900/20 transition-colors"
                >
                  + Ajouter
                </BadgeButton>
              )}
            </div>
          </div>
        </div>

        <Divider />

        <div>
          <h2 className="text-xl font-semibold mb-3 dark:text-white">Filtres</h2>
          <div className='grid grid-cols-2  dark:text-white'>
            <SwitchGroup>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>N'envoyer de messages qu'aux nouvelles personnes</label>
                <Description className='w-96'>Si ce réglage est activé, Anyset n'enverra que des messages à des prospects jamais contactées.</Description>
              </Headless.Field>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>Filtrer par abonnements</label>
                <Description className='w-96'>Si ce réglage est activé, la recherche ne prendra en compte uniquement les prospects entre les deux valeurs.</Description>
                <div className='flex gap-3 mt-3 items-center'>
                  <Field>
                    <Input className="max-w-24" placeholder="100" pattern="[0-9]*" />
                  </Field>
                  <Text>à</Text>
                  <Field>
                    <Input className="max-w-24" placeholder="10000" pattern="[0-9]*" />
                  </Field>
                </div>
              </Headless.Field>
            </SwitchGroup>
            <SwitchGroup>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>Liker les posts</label>
                <Description className='w-96'>Si ce réglage est activé, Anyset likera des posts similaires à votre cible pour optimiser la qualité des prospects.</Description>
              </Headless.Field>
              <Headless.Field>
                <Switch name="test" className='mr-2 items-center'></Switch>
                <label>Filtrer par mots clés</label>
                <Description className='w-96'>Si ce réglage est activé, Anyset contactera uniquement les prospects qui ont les mots spécifiés en bio.</Description>
                <div className="mt-3 flex gap-2 flex-wrap items-center">
                  {keywords.map(keyword => (
                    <BadgeButton
                      key={keyword}
                      color="zinc"
                      onClick={() => handleRemoveKeyword(keyword)}
                      className="hover:bg-red-100 dark:hover:bg-red-900/20 transition-colors"
                      title="Cliquer pour supprimer"
                    >
                      {keyword}
                    </BadgeButton>
                  ))}
                  {showKeywordInput ? (
                    <div className="flex items-center gap-2">
                      <Input
                        value={newKeywordInput}
                        onChange={(e) => setNewKeywordInput(e.target.value)}
                        onKeyDown={handleKeywordKeyPress}
                        placeholder="Mot clé"
                        className="w-24 text-sm"
                        autoFocus
                      />
                      <Button
                        color="green"
                        onClick={handleAddKeyword}
                        className="text-xs px-2 py-1"
                      >
                        ✓
                      </Button>
                      <Button
                        color="red"
                        onClick={() => {
                          setShowKeywordInput(false);
                          setNewKeywordInput("");
                        }}
                        className="text-xs px-2 py-1"
                      >
                        ✕
                      </Button>
                    </div>
                  ) : (
                    <BadgeButton
                      color="blue"
                      onClick={() => setShowKeywordInput(true)}
                      className="hover:bg-blue-100 dark:hover:bg-blue-900/20 transition-colors"
                    >
                      + Ajouter
                    </BadgeButton>
                  )}
                </div>
              </Headless.Field>
            </SwitchGroup>
          </div>
        </div>
      </div>
    </SidebarLayout>
  );
};

export default Page;