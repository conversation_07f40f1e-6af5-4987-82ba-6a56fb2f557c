import React from 'react';
import { SidebarLayout } from '../components/sidebar-layout';
import MainSidebar from '../components/MainSidebar';
import { Avatar } from '../components/avatar';
import { Input } from '../components/input';
import { Dropdown, DropdownButton, DropdownMenu, DropdownItem } from '../components/dropdown';
import { Select } from '../components/select';
import { Listbox, ListboxDescription, ListboxLabel, ListboxOption } from '../components/listbox';
import { CheckboxGroup, CheckboxField, Checkbox } from '../components/checkbox';
import { Badge } from '../components/badge';
import { Switch, SwitchField, SwitchGroup } from '../components/switch';
import { Label, Description, Field } from '../components/fieldset';
import { Divider } from '../components/divider';
import { Button } from '../components/button';
import { Text } from '../components/text';
import {
  HomeIcon,
  PlayIcon,
  PlusIcon,
  TrashIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  ChartBarIcon,
  CalendarIcon
} from '@heroicons/react/20/solid';
import * as Headless from '@headlessui/react';

// Import dashboard components
import { DonutChart } from '../components/dashboard/DonutChart';
import { BarChart } from '../components/dashboard/BarChart';
import { LineChart } from '../components/dashboard/LineChart';
import { MessageStats } from '../components/dashboard/MessageStats';
import { StatsCard } from '../components/dashboard/StatsCard';

const Page = () => {
  return (
    <SidebarLayout
      sidebar={<MainSidebar />}
    >
      <div className="space-y-6">
        <div className='flex place-content-between'>
          <h1 className="text-2xl font-semibold dark:text-white">Dashboard</h1>
        </div>
        <Divider />

        {/* Dashboard Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <StatsCard
            title="Messages envoyés"
            value="1,248"
            description="+12% par rapport au mois dernier"
            icon={<EnvelopeIcon className="h-5 w-5" />}
            color="blue"
          />
          <StatsCard
            title="Réponses reçues"
            value="386"
            description="+8% par rapport au mois dernier"
            icon={<ChatBubbleLeftRightIcon className="h-5 w-5" />}
            color="green"
          />
          <StatsCard
            title="Taux de conversion"
            value="30.9%"
            description="+2.4% par rapport au mois dernier"
            icon={<ChartBarIcon className="h-5 w-5" />}
            color="purple"
          />
          <StatsCard
            title="Messages par jour"
            value="42"
            description="-5% par rapport au mois dernier"
            icon={<CalendarIcon className="h-5 w-5" />}
            color="yellow"
          />
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <MessageStats messages={1248} responses={386} />
          <DonutChart title="Taux de conversion" percentage={31} color="purple" />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <BarChart
            title="Messages par jour de la semaine"
            data={[
              { label: 'Lun', value: 42 },
              { label: 'Mar', value: 38 },
              { label: 'Mer', value: 55 },
              { label: 'Jeu', value: 47 },
              { label: 'Ven', value: 60 },
              { label: 'Sam', value: 32 },
              { label: 'Dim', value: 28 },
            ]}
            color="blue"
          />
          <LineChart
            title="Évolution des messages (30 derniers jours)"
            data={[
              { label: '1', value: 35 },
              { label: '5', value: 42 },
              { label: '10', value: 38 },
              { label: '15', value: 45 },
              { label: '20', value: 52 },
              { label: '25', value: 48 },
              { label: '30', value: 55 },
            ]}
            color="green"
          />
        </div>
        </div>
    </SidebarLayout>
  );
};

export default Page;