import React from 'react';
import { Input } from '../components/input';
import { Button } from '../components/button';
import { Divider } from '../components/divider';
import { Avatar } from '../components/avatar';

const LoginPage = () => {
  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50">
      <div className="w-full max-w-md rounded-xl bg-white shadow-lg p-8 space-y-6">
        <div className="flex justify-center">
          <Avatar src="src/assets/Group 12.png" className="h-12 w-12" />
        </div>
        <h2 className="text-2xl font-semibold text-center">Se connecter</h2>

        <form className="space-y-4">
          <Input type="email" placeholder="Email" required />
          <Input type="password" placeholder="Mot de passe" required />

          <Button href="/instagram" className="w-full" type="submit">
            Connexion
          </Button>
        </form>

        <Divider />

        <div className="text-sm text-center">
          <a href="#" className="text-blue-600 hover:underline">
            Mot de passe oublié ?
          </a>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;